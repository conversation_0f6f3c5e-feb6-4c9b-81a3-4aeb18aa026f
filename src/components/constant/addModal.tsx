"use client";

import React, { useState } from "react";
import { X, Image as ImageIcon } from "lucide-react";
import Image from "next/image";

interface Field {
  id: string;
  label: string;
  type: "text" | "file";
}

interface AddModalProps {
  title: string;
  fields: Field[];
  onClose: () => void;
  onSubmit: (data: Record<string, any>) => void;
}

export default function AddModal({
  title,
  fields,
  onClose,
  onSubmit,
}: AddModalProps) {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [imagePreview, setImagePreview] = useState<Record<string, string>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, id: string) => {
    if (e.target.type === "file" && e.target.files?.[0]) {
      const file = e.target.files[0];
      setFormData((prev) => ({ ...prev, [id]: file }));

      const reader = new FileReader();
      reader.onload = () =>
        setImagePreview((prev) => ({ ...prev, [id]: reader.result as string }));
      reader.readAsDataURL(file);
    } else {
      setFormData((prev) => ({ ...prev, [id]: e.target.value }));
    }
  };

  const handleSubmit = () => {
    onSubmit(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>

      {/* Modal */}
      <div className="relative bg-white/10 backdrop-blur-xl border border-white/20 shadow-xl rounded-2xl p-6 w-full max-w-2xl z-10">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-white">{title}</h2>
          <button onClick={onClose}>
            <X className="w-6 h-6 text-white hover:text-gray-300" />
          </button>
        </div>

        {/* Dynamic Fields */}
        <div className="space-y-4">
          {fields.map((field) => (
            <div key={field.id}>
              <label className="block text-sm text-gray-300 mb-2">
                {field.label}
              </label>

              {field.type === "text" && (
                <input
                  type="text"
                  placeholder={`Enter ${field.label}`}
                  onChange={(e) => handleChange(e, field.id)}
                  className="w-full p-2 rounded-lg bg-black/30 text-white border border-white/20 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                />
              )}

              {field.type === "file" && (
                <div className="relative border border-dashed border-gray-500 rounded-lg p-4 flex items-center justify-center cursor-pointer hover:bg-white/5">
                  <input
                    type="file"
                    accept="image/*"
                    className="absolute inset-0 opacity-0 cursor-pointer"
                    onChange={(e) => handleChange(e, field.id)}
                  />
                  {imagePreview[field.id] ? (
                    <Image
                      src={imagePreview[field.id]}
                      alt="Preview"
                      className="max-h-40 rounded-lg object-contain"
                      width={500}
                      height={200}
                    />
                  ) : (
                    <ImageIcon className="w-10 h-10 text-gray-400" />
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-black rounded-lg hover:bg-gray-400"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Add
          </button>
        </div>
      </div>
    </div>
  );
}
