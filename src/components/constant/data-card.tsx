"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import type { ReactNode } from "react";

interface DataCardProps {
  id: string | undefined;
  avatar?: string;
  fallback?: string;
  title: string;
  subtitle?: string;
  badges?: { label: string; className?: string; dotColor?: string }[];
  actions?: ReactNode; // Buttons passed from parent
}

export function DataCard({
  id,
  avatar,
  fallback,
  title,
  subtitle,
  badges = [],
  actions,
}: DataCardProps) {
  return (
    <Card
      key={id}
      className="w-full bg-[#FFFFFF]/4 border border-[#fff]/40 hover:bg-bg-card-hover text-text-primary hover:shadow-md transition-all duration-200"
    >
      <CardContent className="p-4 w-full">
        {/* Top Section */}
        <div className="flex items-center justify-between w-full">
          {/* Avatar + Info */}
          <div className="flex items-center gap-4 w-full">
            <Avatar className="h-14 w-14">
              {avatar ? (
                <AvatarImage src={avatar} />
              ) : (
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {fallback}
                </AvatarFallback>
              )}
            </Avatar>
            <div className="flex-1">
              <h3 className="font-semibold">{title}</h3>
              {subtitle && (
                <p className="text-sm text-text-secondary">{subtitle}</p>
              )}

              {/* Badges */}
              {badges.length > 0 && (
                <div className="flex gap-2 mt-2 flex-wrap">
                  {badges.map((badge, i) => (
                    <Badge key={i} className={cn(badge.className)}>
                      <span
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: badge.dotColor }}
                      ></span>
                      {badge.label}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">{actions}</div>
        </div>
      </CardContent>
    </Card>
  );
}
