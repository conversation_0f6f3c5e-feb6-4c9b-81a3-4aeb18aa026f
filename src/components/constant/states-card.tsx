import React from "react";
import { MoreHorizontal } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: number;
  change: number;
  isPositive?: boolean; // optional, defaults to true
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  isPositive = true,
}) => {
  return (
    <div className="bg-white/4 border border-white/20 rounded-lg p-6 hover:border-gray-600 transition-colors cursor-pointer">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-gray-400 text-sm font-medium">{title}</h3>
        <MoreHorizontal className="w-4 h-4 text-gray-500" />
      </div>
      <div className="space-y-2">
        <div className="text-white text-3xl font-semibold">
          {value.toLocaleString()}
        </div>
        <div
          className={`text-sm ${
            isPositive ? "text-green-400" : "text-red-400"
          }`}
        >
          {isPositive ? "+" : ""}
          {change}%
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
