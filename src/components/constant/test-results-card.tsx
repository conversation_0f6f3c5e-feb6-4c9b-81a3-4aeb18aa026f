import React from "react";
import { Calendar, <PERSON>, Eye } from "lucide-react";

interface TestResultCardProps {
  testName: string;
  date: string;
  sponsor: string;
  scholars: number;
  testType: "weekly" | "quarterly";
  status: "published" | "unpublished";
  onViewResults: () => void;
}

const TestResultCard: React.FC<TestResultCardProps> = ({
  testName,
  date,
  sponsor,
  scholars,
  testType,
  status,
  onViewResults,
}) => {
  const getStatusBadge = () => {
    switch (status) {
      case "published":
        return (
          <span className="inline-flex items-center px-3 py-1 text-sm font-medium text-white bg-green-600 rounded-sm">
            ✓ Published
          </span>
        );
      case "unpublished":
        return (
          <span className="inline-flex items-center px-3 py-1 text-sm font-medium text-white bg-red-600 rounded-sm">
            ✕ Unpublished
          </span>
        );
      default:
        return null;
    }
  };

  const getTestTypeBadge = () => {
    const isWeekly = testType === "weekly";
    return (
      <span
        className={`inline-flex items-center px-3 py-1 text-sm font-medium rounded-sm ${
          isWeekly ? "text-white bg-green-600" : "text-white bg-orange-500"
        }`}
      >
        {isWeekly ? "Weekly" : "Quarterly"}
      </span>
    );
  };

  return (
    <div className="bg-white/4 border border-white/20 rounded-lg p-6 hover:border-gray-600 transition-colors">
      <h3 className="text-white text-lg font-semibold mb-4">{testName}</h3>

      <div className="space-y-4">
        {/* Date */}
        <div className="flex items-center text-gray-400 text-sm">
          <Calendar className="w-4 h-4 mr-2" />
          {date}
        </div>

        {/* Sponsor and badges + button in same row */}
        <div className="flex items-center w-full">
          {/* Sponsor centered */}
          <div className="flex-1 text-center text-gray-400 text-sm font-medium">
            Sponsored by <span className="text-white">{sponsor}</span>
          </div>

          {/* Badges and button aligned to end */}
          <div className="flex items-center gap-2 ml-auto">
            {getTestTypeBadge()}
            {getStatusBadge()}
            <button
              onClick={onViewResults}
              className="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 bg-gray-200 rounded-sm hover:bg-gray-300 transition-colors"
            >
              <Eye className="w-3 h-3 mr-1" />
              View Results
            </button>
          </div>
        </div>

        {/* Scholars */}
        <div className="flex items-center text-gray-400 text-sm">
          <Users className="w-4 h-4 mr-2" />
          {scholars.toLocaleString()} Scholars
        </div>
      </div>
    </div>
  );
};

export default TestResultCard;
