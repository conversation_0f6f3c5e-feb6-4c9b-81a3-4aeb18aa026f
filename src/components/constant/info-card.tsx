"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import React from "react";

interface InfoItem {
  label: string;
  value: React.ReactNode;
}

interface InfoCardProps {
  title: string;
  icon?: React.ReactNode;
  items: InfoItem[];
}

export function InfoCard({ title, icon, items }: InfoCardProps) {
  return (
    <Card className="bg-zinc-900 border border-zinc-800 rounded-lg shadow-md w-full">
      <CardHeader>
        <CardTitle className="text-base font-semibold text-white flex items-center gap-2">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 text-sm text-white">
          {items.map((item, index) => (
            <div key={index} className="flex justify-between">
              <span className="text-zinc-400">{item.label}:</span>
              <span className="font-semibold">{item.value}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
