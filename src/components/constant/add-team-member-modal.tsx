"use client";

import React, { useState } from "react";
import { X, Penci<PERSON>, Check } from "lucide-react";
import axios from "axios";
import { UserRole } from "@/models/user";
import { toast } from "sonner";

interface TeamMemberFormProps {
  mode: "create" | "edit";
  initialData?: {
    name?: string;
    id?: string;
    email?: string;
    password?: string;
    roles?: UserRole[];
  };
  onClose: () => void;
  onSubmit: (data: {
    name: string;
    email: string;
    password: string;
    roles: string;
  }) => void;
}

export default function TeamMemberForm({
  mode,
  initialData,
  onClose,
  onSubmit,
}: TeamMemberFormProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    id: initialData?.id || "",
    email: initialData?.email || "",
    password: initialData?.password || "",
    roles: initialData?.roles
      ? Array.isArray(initialData.roles)
        ? initialData.roles[0] || "USER"
        : initialData.roles
      : "USER",
  });

  const [errors, setErrors] = useState({
    name: "",
    email: "",
    password: "",
    roles: "",
  });
  const [isPasswordEditing, setIsPasswordEditing] = useState(mode === "create");
  const [passwordSnapshot, setPasswordSnapshot] = useState(formData.password);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    const newErrors = { name: "", email: "", password: "", roles: "" };
    let valid = true;

    if (!formData.name.trim()) {
      newErrors.name = "Full Name is required";
      valid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
      valid = false;
    } else if (
      !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)
    ) {
      newErrors.email = "Invalid email address";
      valid = false;
    }

    const requirePassword = mode === "create" || isPasswordEditing;
    if (requirePassword && !formData.password.trim()) {
      newErrors.password = "Password is required";
      valid = false;
    }

    if (!formData.roles.trim()) {
      newErrors.roles = "Role is required";
      valid = false;
    }

    setErrors(newErrors);

    if (!valid) return; // Stop submission if invalid

    onSubmit(formData);
    // onClose();
  };

  const handleResetPassword = async () => {
    // Validate password before submitting
    if (!formData.password.trim()) {
      toast.error("Password is required", {
        description: "Please enter a new password before confirming.",
      });
      return;
    }

    try {
      const res = await axios.post("/api/auth/user/reset-password", {
        userId: formData.id,
        password: formData.password,
      });

      if (res.status === 200) {
        setIsPasswordEditing(false);
        toast.success("Password reset successfully!", {
          description: "The user's password has been updated.",
        });
      }
    } catch (error: any) {
      toast.error("Failed to reset password", {
        description:
          error?.response?.data?.message ||
          "An error occurred while resetting the password.",
      });
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
      <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 shadow-xl rounded-2xl p-8 w-full max-w-2xl z-10">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-white capitalize">
            {mode === "create" ? "Add New Team Member" : "Edit Team Member"}
          </h2>
          <button onClick={onClose}>
            <X className="w-6 h-6 text-white hover:text-gray-300" />
          </button>
        </div>

        {/* Form Fields */}
        <div className="space-y-4 flex-1 overflow-y-auto p-2">
          <div>
            <label className="block text-sm text-gray-300 mb-2">
              Full Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter full name"
              className={`w-full p-2 rounded-lg bg-black/30 text-white border ${
                errors.name ? "border-red-500" : "border-white/20"
              } focus:outline-none focus:ring-2 focus:ring-cyan-400`}
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm text-gray-300 mb-2">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter email"
              className={`w-full p-2 rounded-lg bg-black/30 text-white border ${
                errors.email ? "border-red-500" : "border-white/20"
              } focus:outline-none focus:ring-2 focus:ring-cyan-400`}
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm text-gray-300">Password</label>
              {mode === "edit" && !isPasswordEditing ? (
                <button
                  type="button"
                  onClick={() => {
                    setPasswordSnapshot(formData.password);
                    setIsPasswordEditing(true);
                  }}
                  className="text-xs text-cyan-400 hover:text-cyan-300 flex items-center gap-1"
                >
                  <Pencil className="w-4 h-4" /> Edit
                </button>
              ) : mode === "edit" && isPasswordEditing ? (
                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    onClick={handleResetPassword}
                    className="text-green-400 hover:text-green-300"
                    title="Confirm"
                  >
                    <Check className="w-5 h-5" />
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setFormData((prev) => ({
                        ...prev,
                        password: passwordSnapshot,
                      }));
                      setIsPasswordEditing(false);
                      setErrors((prev) => ({ ...prev, password: "" }));
                    }}
                    className="text-red-400 hover:text-red-300"
                    title="Cancel"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              ) : null}
            </div>

            {mode === "edit" && !isPasswordEditing ? (
              <div className="w-full p-2 rounded-lg bg-black/30 text-white border border-white/20">
                ••••••••
              </div>
            ) : (
              <input
                type="text"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder={
                  mode === "edit" ? "Enter new password" : "Enter password"
                }
                className={`w-full p-2 rounded-lg bg-black/30 text-white border ${
                  errors.password ? "border-red-500" : "border-white/20"
                } focus:outline-none focus:ring-2 focus:ring-cyan-400`}
              />
            )}
            {errors.password && (
              <p className="text-red-500 text-sm mt-1">{errors.password}</p>
            )}
          </div>

          <div>
            <label className="block text-sm text-gray-300 mb-2">Role</label>
            <select
              name="roles"
              value={formData.roles}
              onChange={handleChange}
              className={`w-full p-2 rounded-lg bg-black/30 text-white border ${
                errors.roles ? "border-red-500" : "border-white/20"
              } focus:outline-none focus:ring-2 focus:ring-cyan-400`}
            >
              <option value="SUPER_ADMIN">Super Admin</option>
              <option value="ADMIN">Admin</option>
              <option value="USER">User</option>
              <option value="CONTENT_CREATOR">Content Creator</option>
            </select>
            {errors.roles && (
              <p className="text-red-500 text-sm mt-1">{errors.roles}</p>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-black rounded-lg hover:bg-gray-400"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            {mode === "create" ? "Create" : "Save Changes"}
          </button>
        </div>
      </div>
    </div>
  );
}
