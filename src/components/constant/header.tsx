"use client";

import { FC, useState, useRef, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface HeaderProps {
  title?: string;
  userName?: string;
}

const Header: FC<HeaderProps> = ({ title, userName = "User" }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleLogout = async () => {
    console.log("Logout clicked");
    fetch("/api/auth/log-out", {
      method: "POST",
    })
      .then((res) => {
        if (res.ok) {
          window.location.href = "/";
        }
      })
      .catch((err) => {
        console.error("Error logging out:", err);
      });
  };

  // Avatar fallback (first letter)
  const initial = userName.charAt(0).toUpperCase();

  return (
    <header className="fixed top-0 left-0 right-0 h-14 bg-zinc-900 border-b border-zinc-800 flex items-center justify-between px-6 z-50">
      {/* Left: Logo + Title */}
      <div className="flex items-center gap-2">
        <div className="h-8 w-8 bg-primary rounded-md flex items-center justify-center font-bold">
          L
        </div>
        <span className="font-semibold">{title ?? "MyApp"}</span>
      </div>

      {/* Right: Avatar + Dropdown */}
      <div className="relative flex items-center gap-4" ref={menuRef}>
        <span className="text-sm">{userName}</span>

        <button
          onClick={() => setIsMenuOpen((prev) => !prev)}
          className="focus:outline-none"
        >
          <Avatar className="h-8 w-8 border border-zinc-600">
            {/* No image, just fallback */}
            <AvatarFallback className="bg-zinc-700 text-white font-semibold">
              {initial}
            </AvatarFallback>
          </Avatar>
        </button>

        {/* Dropdown Menu */}
        {isMenuOpen && (
          <div className="absolute right-0 top-12 w-36 bg-zinc-800 border border-zinc-700 rounded-md shadow-lg">
            <button
              onClick={handleLogout}
              className="w-full text-left px-4 py-2 text-sm hover:bg-zinc-700 transition"
            >
              Logout
            </button>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
