'use client';
import { Sidebar } from "@/components/constant/side-bar";
import Header from "@/components/constant/header";
import React, { ReactNode } from "react";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";

interface MainLayoutProps {
  children: ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const AuthUserData = useSelector((state: RootState) => state.constantReducer.authUser);
  return (
    <div className="flex h-screen bg-[#0E0E0E] text-white">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header title="Dashboard" userName={AuthUserData?.name} />

        {/* Page Content */}
        <main className="flex-1 mt-14 p-6 overflow-auto main-content">
          <div className="full-width-container">{children}</div>
        </main>
      </div>
    </div>
  );
}
