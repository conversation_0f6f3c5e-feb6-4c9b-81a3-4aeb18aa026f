declare module "react-infinite-scroller" {
  import * as React from "react";

  export interface InfiniteScrollProps {
    pageStart?: number;
    loadMore: (page: number) => void;
    hasMore?: boolean;
    initialLoad?: boolean;
    threshold?: number;
    useWindow?: boolean;
    isReverse?: boolean;
    getScrollParent?: () => HTMLElement | null;
    useCapture?: boolean;
    element?: string | React.ComponentType<any>;
    loader?: React.ReactNode;
    children?: React.ReactNode;
    className?: string;
    style?: React.CSSProperties;
  }

  export default class InfiniteScroll extends React.Component<
    InfiniteScrollProps
  > {}
}

