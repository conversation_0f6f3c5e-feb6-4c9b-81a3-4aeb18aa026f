// src/lib/verifyToken.ts
import jwt from "jsonwebtoken";
import axios from "axios";

const REFRESH_API_URL = `${process.env.API_URL}/api/auth/refresh`;

interface VerifyResult {
  valid: boolean;
  accessToken?: string;
  refreshToken?: string;
}

export async function verifyAndRefreshToken(
  accessToken?: string,
  refreshToken?: string
): Promise<VerifyResult> {
  // No token at all
  if (!accessToken) {
    return { valid: false };
  }

  // Helper: check expiry
  const isTokenExpired = (token: string): boolean => {
    try {
      const decoded = jwt.decode(token) as { exp?: number };
      if (!decoded?.exp) return true;
      const now = Math.floor(Date.now() / 1000);
      return decoded.exp < now;
    } catch {
      return true;
    }
  };

  // If still valid → done
  if (!isTokenExpired(accessToken)) {
    return { valid: true, accessToken, refreshToken };
  }

  // If expired, try refresh
  if (refreshToken) {
    try {
      const res = await axios.post(
        REFRESH_API_URL,
        { refreshToken },
        { headers: { "Content-Type": "application/json" } }
      );

      const newAccessToken = res.data?.accessToken;
      const newRefreshToken = res.data?.refreshToken;

      if (newAccessToken) {
        return {
          valid: true,
          accessToken: newAccessToken,
          refreshToken: newRefreshToken || refreshToken,
        };
      }
    } catch (err) {
      console.error("Token refresh failed:", err);
    }
  }

  // If all failed
  return { valid: false };
}
