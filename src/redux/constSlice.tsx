import { User } from "@/models/user";
import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";

interface ConstState {
  authUser: User;
}

const initialState: ConstState = {
  authUser: {
    id: "",
    mobileNumber: "",
    name: "",
    email: "",
    roles: [],
    status: "",
    lastLoginAt: "",
  },
};

const constSlice = createSlice({
  name: "constant",
  initialState,
  reducers: {
    setAuthUser: (state, action: PayloadAction<User>) => {
      state.authUser = action.payload;
    },
  },
});

export const { setAuthUser } = constSlice.actions;

export default constSlice.reducer;
