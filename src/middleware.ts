import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function middleware(req: NextRequest) {
  const { cookies } = req;
  const token = cookies.get("accessToken")?.value;
  const { pathname } = req.nextUrl;

  const protectedPaths: string[] = [
    "/dashboard",
    "/subscribers",
    "/leUsers",
    "/content",
    "/finance",
    "/sponsorAds",
    "/results",
    "/auditLog",
    "/masterData",
  ];
  const publicPaths = ["/"];

  const isProtectedRoute = protectedPaths.some((path) =>
    pathname.startsWith(path)
  );
  const isPublicPath = publicPaths.includes(pathname);
  const isApiRoute = pathname.startsWith("/api");

  // Skip middleware for API routes
  if (isApiRoute) {
    return NextResponse.next();
  }

  // If unauthenticated and not accessing public route, redirect to '/'
  if (!token && !isPublicPath) {
    return NextResponse.redirect(new URL("/", req.url));
  }

  // If already authenticated and accessing public route, redirect to dashboard
  if (token && isPublicPath) {
    return NextResponse.redirect(new URL("/dashboard", req.url));
  }

  // If trying to access protected route without token
  if (isProtectedRoute && !token) {
    return NextResponse.redirect(new URL("/", req.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico).*)", // Allow everything except static/image/favicon
  ],
};
