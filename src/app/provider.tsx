"use client"; // This directive marks the component as a client component

import store, { persistor } from "@/redux/store";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { Toaster } from "sonner";

const ClientProvider = ({ children }: any) => (
  <Provider store={store}>
    <PersistGate loading={null} persistor={persistor}>
      {children}
      <Toaster position="top-right" richColors />
    </PersistGate>
  </Provider>
);

export default ClientProvider;
