"use client";

import MainLayout from "@/layouts/main-layout";
import React, { useState } from "react";
import { TabNavigation } from "@/components/constant/tab-navigation";
import FinanceDashboard from "./finance-dashboard";

const tabs = [
  { id: "financeDashboard", label: "Finance Dasboard" },
  {
    id: "transactions",
    label: "Transactions",
  },
  { id: "search", label: "Search" },
];

export default function FinancePage() {
  // Default date range: past 7 days
  const [activeTab, setActiveTab] = useState<string>("financeDashboard");

  const renderTabContent = () => {
    switch (activeTab) {
      case "financeDashboard":
        return <FinanceDashboard />;
      case "overview":
        return;
      case "search":
        return (
          <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-lg p-8 text-center text-gray-300">
            Credits content goes here
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <MainLayout>
      <div className="">
        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
      {renderTabContent()}
    </MainLayout>
  );
}
