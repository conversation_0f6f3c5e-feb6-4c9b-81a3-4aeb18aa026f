import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

const backendUrl = process.env.API_URL;

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Read token from cookies
    const token = request.cookies.get("accessToken")?.value;

    // Optionally call backend logout endpoint (even if no token)
    if (token && backendUrl) {
      try {
        await axios.post(
          `${backendUrl}/auth/logout`,
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
      } catch (error) {
        // Log but don't fail - we still want to clear cookies
        console.error("Backend logout failed:", error);
      }
    }

    // Create response
    const response = NextResponse.json(
      { message: "Logged out successfully" },
      { status: 200 }
    );

    // Clear all auth cookies
    response.cookies.set("accessToken", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/",
      expires: new Date(0), // Expire immediately
    });

    response.cookies.set("refreshToken", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/",
      expires: new Date(0),
    });

    response.cookies.set("roles", "", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    console.log("✅ User logged out and cookies cleared.");

    return response;
  } catch (error: any) {
    console.error(
      "Error during logout:",
      error?.response?.data || error.message
    );

    // Even on error, try to clear cookies
    const response = NextResponse.json(
      {
        message: "Logged out (with errors)",
      },
      {
        status: 200, // Still return 200 so frontend can redirect
      }
    );

    // Clear cookies anyway
    response.cookies.set("accessToken", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/",
      expires: new Date(0),
    });

    response.cookies.set("refreshToken", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/",
      expires: new Date(0),
    });

    response.cookies.set("roles", "", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    return response;
  }
}
