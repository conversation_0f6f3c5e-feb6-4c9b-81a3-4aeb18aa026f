import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

const backendUrl = process.env.API_URL; // e.g. "https://api.yourbackend.com"

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Read token from cookies
    const token = request.cookies.get("accessToken")?.value;

    if (!token) {
      return NextResponse.json(
        { message: "Unauthorized: No token found" },
        { status: 401 }
      );
    }

    // Call your backend API to get current user details
    const response = await axios.get(`${backendUrl}/auth/me`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    // Return backend response to frontend
    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error(
      "Error fetching user details:",
      error?.response?.data || error.message
    );

    return NextResponse.json(
      {
        message:
          error?.response?.data?.message || "Failed to fetch user details",
      },
      {
        status: error?.response?.status || 500,
      }
    );
  }
}
