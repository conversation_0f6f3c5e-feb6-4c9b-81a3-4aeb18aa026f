import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

const backendUrl = process.env.API_URL;

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const token = request.cookies.get("accessToken")?.value;

    console.log("body", body);
    console.log("token", token);

    if (!token) {
      return NextResponse.json(
        { message: "Unauthorized: No token found" },
        { status: 401 }
      );
    }

    const { userId, status } = body;

    if (!userId || !status) {
      return NextResponse.json(
        { message: "Missing userId or status" },
        { status: 400 }
      );
    }

    // Try PATCH method first (RESTful approach to update a single field)
    let response;
    try {
      response = await axios.patch(
        `${backendUrl}/auth/admin/users/${userId}`,
        { status }, // body
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
    } catch (patchError: any) {
      // If PATCH fails with 404 or 405, try POST to /status endpoint
      if (
        patchError?.response?.status === 404 ||
        patchError?.response?.status === 405
      ) {
        console.log("PATCH failed, trying POST to /status endpoint");
        response = await axios.post(
          `${backendUrl}/auth/admin/users/${userId}/status`,
          { status },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
      } else {
        throw patchError;
      }
    }

    console.log("response", response.data);

    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error(
      "Error updating user status:",
      error?.response?.data || error.message
    );

    return NextResponse.json(
      {
        message:
          error?.response?.data?.message || "Failed to update user status",
      },
      {
        status: error?.response?.status || 500,
      }
    );
  }
}
