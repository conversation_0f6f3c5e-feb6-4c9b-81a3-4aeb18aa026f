import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

const backendUrl = process.env.API_URL;

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const token = request.cookies.get("accessToken")?.value;

    console.log("body", body);
    console.log("token", token);

    if (!token) {
      return NextResponse.json(
        { message: "Unauthorized: No token found" },
        { status: 401 }
      );
    }

    const { userId, password } = body;

    if (!userId || !password) {
      return NextResponse.json(
        { message: "Missing userId or password" },
        { status: 400 }
      );
    }

    // Call your backend API to reset user password
    const response = await axios.post(
      `${backendUrl}/auth/admin/users/${userId}/reset-password`,
      { password }, // body
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("response", response.data);

    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error(
      "Error resetting user password:",
      error?.response?.data || error.message
    );

    return NextResponse.json(
      {
        message:
          error?.response?.data?.message || "Failed to reset user password",
      },
      {
        status: error?.response?.status || 500,
      }
    );
  }
}
