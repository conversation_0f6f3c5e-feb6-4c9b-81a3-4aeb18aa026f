import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { jwtDecode } from "jwt-decode"; // 📦 decode JWT
import { Auth, AuthResponse } from "@/models/auth";

const backendUrl = process.env.API_URL;

interface JWTPayload {
  exp: number; // expiration timestamp in seconds
  iat?: number;
  [key: string]: any;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = (await request.json()) as Auth;
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        { message: "Email and password are required" },
        { status: 400 }
      );
    }

    const res = await axios.post<AuthResponse>(
      `${backendUrl}/auth/admin/login`,
      { email, password },
      { headers: { "Content-Type": "application/json" } }
    );

    const data = res.data;

    // 🧩 Decode the access token to get exp
    const decoded: JWTPayload = jwtDecode(data.accessToken);
    const expiresAt = new Date(decoded.exp * 1000); // convert seconds to ms

    const response = NextResponse.json(
      { message: "Login successful", user: data.user },
      { status: 200 }
    );

    // ✅ Set cookies using decoded expiration
    response.cookies.set("accessToken", data.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/",
      expires: expiresAt,
    });

    response.cookies.set("refreshToken", data.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/",
      expires: new Date(data.refreshTokenExpiresAt), // still from backend
    });

    const roles = data.user.roles.join(",");
    response.cookies.set("roles", roles, {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
    });

    console.log("✅ Tokens stored securely in cookies with decoded expiry.");

    return response;
  } catch (error: any) {
    console.error("Error processing login request:", error);

    if (error.response) {
      return NextResponse.json(
        { message: error.response.data?.message || "Invalid credentials" },
        { status: error.response.status || 400 }
      );
    }

    return NextResponse.json(
      { message: "An error occurred. Please try again later." },
      { status: 500 }
    );
  }
}
