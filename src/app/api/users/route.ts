import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

const backendUrl = process.env.API_URL;

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const token = request.cookies.get("accessToken")?.value;

    if (!token) {
      return NextResponse.json(
        { message: "Unauthorized: No token found" },
        { status: 401 }
      );
    }

    // Extract pagination params and forward to backend
    const page = request.nextUrl.searchParams.get("page");
    const size = request.nextUrl.searchParams.get("size");

    // Call backend API to get all users with optional pagination
    const backendResponse = await axios.get(`${backendUrl}/auth/admin/users`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        ...(page ? { page: Number(page) } : {}),
        ...(size ? { size: Number(size) } : {}),
      },
    });

    const data = backendResponse.data;

    // Return user list to frontend
    return NextResponse.json(
      {
        message: "Users fetched successfully",
        users: data.users || data.user || data, // handles different response shapes
      },
      { status: 200 }
    );
  } catch (error: unknown) {
    console.error("Error fetching users:", error);

    if (axios.isAxiosError(error) && error.response) {
      return NextResponse.json(
        {
          message: error.response.data?.message || "Failed to fetch users",
        },
        { status: error.response.status || 400 }
      );
    }

    return NextResponse.json(
      { message: "An error occurred. Please try again later." },
      { status: 500 }
    );
  }
}
