import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

const backendUrl = process.env.API_URL; // e.g. "https://api.yourbackend.com"

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Read token from cookies
    const body = await request.json();
    const token = request.cookies.get("accessToken")?.value;

    console.log("body", body);
    console.log("token", token);

    if (!token) {
      return NextResponse.json(
        { message: "Unauthorized: No token found" },
        { status: 401 }
      );
    }

    // Call your backend API to get current user details
    const response = await axios.post(
      `${backendUrl}/auth/admin/users`, // URL
      body, // Request body
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    console.log("respomse", response);

    // Return backend response to frontend
    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error(
      "Error Creating user:",
      error?.response?.data || error.message
    );

    return NextResponse.json(
      {
        message: error?.response?.data?.message || "Failed to Create user",
      },
      {
        status: error?.response?.status || 500,
      }
    );
  }
}
