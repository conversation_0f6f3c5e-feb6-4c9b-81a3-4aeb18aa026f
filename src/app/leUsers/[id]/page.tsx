"use client";
import {
  Column,
  StatusBadge,
  DynamicDataTable,
} from "@/components/constant/dynamic-data-table";
import { TabNavigation } from "@/components/constant/tab-navigation";
import MainLayout from "@/layouts/main-layout";
import React, { useState } from "react";
import { SubscriberProfileHeader } from "./subscriber-profile-header";
import SubscriberProfileOverview from "./subsciber-profile-overview";

interface Transaction {
  transactionId: string;
  date: string;
  amount: number;
  paymentMode: string;
  status: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  joinedDate: string;
  lastActive: string;
  status: "Active" | "Inactive";
  plan: "Pro" | "Basic" | "Premium";
}
// Sample data
const sampleUser: User = {
  id: "1",
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+91 6282745361",
  avatar: "/professional-headshot.png",
  joinedDate: "15-08-2025",
  lastActive: "20-01-2025",
  status: "Active",
  plan: "Pro",
};

const sampleTransactions: Transaction[] = [
  {
    transactionId: "************",
    date: "20-01-2024, 16:00:00",
    amount: 99,
    paymentMode: "UPI",
    status: "Pending",
  },
  {
    transactionId: "************",
    date: "21-01-2024, 09:30:00",
    amount: 0,
    paymentMode: "Card",
    status: "Cancelled",
  },
  {
    transactionId: "************",
    date: "22-01-2024, 12:15:00",
    amount: 256,
    paymentMode: "Net Banking",
    status: "Done",
  },
  {
    transactionId: "************",
    date: "23-01-2024, 14:45:00",
    amount: 2,
    paymentMode: "Card",
    status: "Pending",
  },
  {
    transactionId: "************",
    date: "24-01-2024, 11:00:00",
    amount: 407,
    paymentMode: "Card",
    status: "Done",
  },
  {
    transactionId: "************",
    date: "25-01-2024, 17:30:00",
    amount: 0,
    paymentMode: "UPI",
    status: "Cancelled",
  },
  {
    transactionId: "************",
    date: "26-01-2024, 08:00:00",
    amount: 178,
    paymentMode: "Card",
    status: "Done",
  },
];

const tabs = [
  { id: "contentCreated", label: "Content Created" },
  {
    id: "activityLog",
    label: "Activity Log",
    count: sampleTransactions.length,
  },
];

const transactionColumns: Column<Transaction>[] = [
  {
    key: "transactionId",
    label: "Transaction ID",
    sortable: true,
    className: "font-mono",
  },
  {
    key: "date",
    label: "Date",
    sortable: true,
  },
  {
    key: "amount",
    label: "Amount",
    sortable: true,
    render: (value) => <span className="font-medium">{value}</span>,
  },
  {
    key: "paymentMode",
    label: "Payment Mode",
    sortable: true,
  },
  {
    key: "status",
    label: "Status",
    sortable: true,
    render: (value) => <StatusBadge status={value} />,
  },
];

export default function SubscriberDetailPage() {
  const [activeTab, setActiveTab] = useState<string>("contentCreated");

  const handleAddCredit = () => {
    console.log("Add credit clicked");
  };

  const handleEditUser = () => {
    console.log("Edit user clicked");
  };

  const handleRowClick = (transaction: Transaction) => {
    console.log("Transaction clicked:", transaction);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "activityLog":
        return (
          <DynamicDataTable
            data={sampleTransactions}
            columns={transactionColumns}
            onRowClick={handleRowClick}
          />
        );
      case "contentCreated":
        return <SubscriberProfileOverview teamMember={sampleUser} />;
      default:
        return null;
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-white">Scholer Details</h1>

        <SubscriberProfileHeader
          user={sampleUser}
          onAddCredit={handleAddCredit}
          onEditUser={handleEditUser}
        />

        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />

        {renderTabContent()}
      </div>
    </MainLayout>
  );
}
