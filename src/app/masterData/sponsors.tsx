import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Edit, Trash2, Gift, Mountain } from "lucide-react";

const SponsorManagement = () => {
  const [formData, setFormData] = useState({
    sponsorName: "",
    websiteUrl: "",
    termsConditions: "",
    logo: null,
  });

  type Reward = {
    title: string;
    description: string;
    details: string[];
    tnc: string[];
  };

  type Sponsor = {
    id: number;
    sponsorName: string;
    websiteUrl: string;
    termsConditions: string;
    reward: Reward | null;
  };

  const [sponsors, setSponsors] = useState<Sponsor[]>([
    {
      id: 1,
      sponsorName: "HDFC Bank",
      websiteUrl: "https://example12345.com",
      termsConditions: "Sample terms and conditions...",
      reward: {
        title: "Reward Coupon",
        description: "1000 on Amazon",
        details: [
          "Coupon Code: EDU20",
          "Applicable on: All premium courses",
          "Issued to: [Student Name]",
          "Expiry Date: Valid till 30 Sept 2025",
        ],
        tnc: [
          "This coupon is valid for one-time use only.",
          "Cannot be combined with other offers.",
          "Non-transferable and non-refundable.",
          "Valid only on in-app purchases.",
          "Expired coupons will not be reissued.",
        ],
      },
    },
    {
      id: 2,
      sponsorName: "HDFC Bank",
      websiteUrl: "https://example12345.com",
      termsConditions: "Sample terms and conditions...",
      reward: {
        title: "Reward Coupon",
        description: "1000 on Amazon",
        details: [
          "Coupon Code: EDU20",
          "Applicable on: All premium courses",
          "Issued to: [Student Name]",
          "Expiry Date: Valid till 30 Sept 2025",
        ],
        tnc: [
          "This coupon is valid for one-time use only.",
          "Cannot be combined with other offers.",
          "Non-transferable and non-refundable.",
          "Valid only on in-app purchases.",
          "Expired coupons will not be reissued.",
        ],
      },
    },
  ]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    if (!formData.sponsorName || !formData.websiteUrl) return;
    const newSponsor = {
      id: Date.now(),
      sponsorName: formData.sponsorName,
      websiteUrl: formData.websiteUrl,
      termsConditions: formData.termsConditions,
      reward: null,
    };
    setSponsors((prev) => [...prev, newSponsor]);
    handleCancel();
  };

  const handleCancel = () => {
    setFormData({
      sponsorName: "",
      websiteUrl: "",
      termsConditions: "",
      logo: null,
    });
  };

  const handleEdit = (id: number) => {
    console.log("Edit sponsor", id);
  };

  const handleDelete = (id: number) => {
    setSponsors((prev) => prev.filter((s) => s.id !== id));
  };

  return (
    <div className="mt-6 mx-auto space-y-8">
      {/* Add New Sponsor Form */}
      <Card className="bg-[#0E0E0E] border border-none">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Sponsor Name */}
            <div className="space-y-2 w-3/4">
              <Label
                htmlFor="sponsorName"
                className="text-sm font-medium text-white"
              >
                Sponsor Name
              </Label>
              <Input
                id="sponsorName"
                name="sponsorName"
                value={formData.sponsorName}
                onChange={handleInputChange}
                placeholder="HDFC Bank"
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-500"
              />
            </div>

            {/* Website URL */}
            <div className="space-y-2 w-3/4">
              <Label
                htmlFor="websiteUrl"
                className="text-sm font-medium text-white"
              >
                Website URL
              </Label>
              <Input
                id="websiteUrl"
                name="websiteUrl"
                type="url"
                value={formData.websiteUrl}
                onChange={handleInputChange}
                placeholder="https://example.com"
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Sponsor Logo */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-white">
                Sponsor Logo
              </Label>
              <div className="flex items-center gap-4">
                <div className="bg-zinc-800 border border-zinc-700 rounded-lg flex items-center justify-center w-3/4 h-40">
                  <div className="w-14 h-14 border-2 border-zinc-600 rounded flex items-center justify-center">
                    <Mountain className="w-6 h-6 text-zinc-500" />
                  </div>
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-zinc-200 text-zinc-800 hover:bg-zinc-300"
                >
                  <Edit className="w-3 h-3 mr-1" /> Edit
                </Button>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="space-y-2 w-3/4">
              <Label
                htmlFor="termsConditions"
                className="text-sm font-medium text-white"
              >
                Terms and Condition
              </Label>
              <Textarea
                id="termsConditions"
                name="termsConditions"
                value={formData.termsConditions}
                onChange={handleInputChange}
                placeholder="Enter terms and conditions"
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-500 min-h-[120px] resize-none"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="bg-transparent border-zinc-700 text-white hover:bg-zinc-800"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Add
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Existing Sponsors Section */}
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold text-white">
            Existing Sponsors
          </h2>
          <p className="text-sm text-zinc-400">
            Manage your sponsor partnerships
          </p>
        </div>

        {sponsors.map((sponsor) => (
          <Card key={sponsor.id} className="bg-white/4 border border-zinc-800">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left Content */}
                <div className="lg:col-span-2 space-y-2">
                  <div className="text-base font-semibold text-white">
                    Sponsor Name : {sponsor.sponsorName}
                  </div>
                  <div className="text-sm text-zinc-400">
                    Website URL : {sponsor.websiteUrl}
                  </div>
                  <div className="text-sm text-zinc-400 mt-2">
                    Terms and Conditions :
                  </div>

                  {sponsor.reward && (
                    <div className="mt-4 pt-4 border-t border-zinc-800">
                      <div className="flex items-center gap-2 mb-3">
                        <Gift className="w-4 h-4 text-orange-500" />
                        <span className="text-sm font-semibold text-white">
                          {sponsor.reward.title}
                        </span>
                      </div>
                      <div className="text-base font-semibold text-white mb-3">
                        {sponsor.reward.description}
                      </div>
                      <div className="space-y-3">
                        <div>
                          <div className="text-xs font-semibold text-white mb-1">
                            Details Section:
                          </div>
                          <ul className="text-xs text-zinc-400 space-y-0.5 ml-4">
                            {sponsor.reward.details.map((d, i) => (
                              <li key={i}>• {d}</li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <div className="text-xs font-semibold text-white mb-1">
                            Terms & Conditions:
                          </div>
                          <div className="text-xs text-zinc-400 leading-relaxed">
                            {sponsor.reward.tnc.map((t, i) => (
                              <div key={i}>
                                {i + 1}. {t}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Right Content */}
                <div className="flex flex-col items-end space-y-3">
                  <div className="w-32 h-24 bg-zinc-800 border border-zinc-700 rounded flex items-center justify-center">
                    <div className="w-12 h-12 border-2 border-zinc-600 rounded flex items-center justify-center">
                      <Mountain className="w-5 h-5 text-zinc-500" />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(sponsor.id)}
                      className="bg-transparent border-zinc-700 text-white hover:bg-zinc-800"
                    >
                      <Edit className="w-3 h-3 mr-1" /> Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(sponsor.id)}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      <Trash2 className="w-3 h-3 mr-1" /> Delete
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SponsorManagement;
