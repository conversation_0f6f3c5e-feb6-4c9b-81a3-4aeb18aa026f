"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent } from "@/components/ui/card";
import { format } from "date-fns";
import { Calendar as CalendarIcon, Edit, Trash2 } from "lucide-react";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";

export default function TimeTable() {
  const [date, setDate] = useState<Date>();
  const [open, setOpen] = useState(false); // calendar popover control
  const [examType, setExamType] = useState("Weekly"); // default weekly
  const [timeTables, setTimeTables] = useState([
    { id: 1, name: "Weekly", date: "25-07-25" },
    { id: 2, name: "Monthly", date: "30-08-25" },
    { id: 3, name: "Weekly", date: "15-11-25" },
    { id: 4, name: "Weekly", date: "22-12-25" },
  ]);

  const handleAdd = () => {
    if (!examType || !date) return;
    const newEntry = {
      id: Date.now(),
      name: examType,
      date: format(date, "dd-MM-yy"),
    };
    setTimeTables([...timeTables, newEntry]);
    setExamType("Weekly"); // reset to default
    setDate(undefined);
  };

  return (
    <div className="space-y-6 mt-4">
      {/* Input Section */}
      <div className="flex w-full justify-between items-end gap-6">
        <div className="flex flex-col flex-1">
          <label className="text-sm font-medium text-white mb-2">
            Exam Type
          </label>
          <Select value={examType} onValueChange={setExamType}>
            <SelectTrigger className="w-full bg-zinc-900 border border-zinc-800 text-white">
              <SelectValue placeholder="Select Exam Type" />
            </SelectTrigger>
            <SelectContent className="bg-zinc-900 border border-zinc-800 text-white">
              <SelectItem value="Weekly">Weekly</SelectItem>
              <SelectItem value="Monthly">Monthly</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Exam Date */}
        <div className="flex flex-col flex-1">
          <label className="text-sm font-medium text-white mb-2">
            Exam Date
          </label>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <button className="flex items-center justify-between px-3 py-2 bg-white/4 border border-zinc-800 rounded-md text-sm text-white w-full">
                {date ? format(date, "dd/MM/yy") : "dd/mm/yy"}
                <CalendarIcon className="h-4 w-4 opacity-70" />
              </button>
            </PopoverTrigger>
            <PopoverContent className="p-0 bg-white/10 border border-zinc-800 rounded-xl">
              <Calendar
                mode="single"
                selected={date}
                onSelect={(day) => {
                  setDate(day);
                  setOpen(false); // close calendar after selection
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex justify-end gap-3">
        <Button
          className="rounded-md bg-white text-[#8A979B] px-5 py-2"
          onClick={() => {
            setExamType("Weekly");
            setDate(undefined);
          }}
        >
          Cancel
        </Button>
        <Button
          className="bg-green-600 hover:bg-green-700 rounded-md px-6 py-2"
          onClick={handleAdd}
        >
          Add
        </Button>
      </div>

      {/* Time Table List */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-white">Time Table</h2>
        <p className="text-sm text-zinc-400">Manage Existing all Timetable</p>

        {timeTables.map((item) => (
          <Card
            key={item.id}
            className="bg-zinc-900 border border-zinc-800 rounded-xl shadow-sm"
          >
            <CardContent className="flex justify-between items-center p-4">
              <div className="space-y-1 text-white text-sm">
                <p>
                  <span className="font-medium">Exam Type :- </span>
                  {item.name}
                </p>
                <p>
                  <span className="font-medium">Exam Date : </span>
                  {item.date}
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-white text-black hover:bg-zinc-700"
                >
                  <Edit className="h-4 w-4 mr-1" /> Edit
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-[#D1E4EB]/64 text-red-500 hover:bg-red-600/20"
                >
                  <Trash2 className="h-4 w-4 mr-1" /> Delete
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
