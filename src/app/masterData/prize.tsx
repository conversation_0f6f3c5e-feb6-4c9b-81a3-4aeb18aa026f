"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Edit, Trash2 } from "lucide-react";
import React, { useState } from "react";

type PrizeForm = {
  prizeName: string;
  prizeType: string;
  prizeValue: string;
};

export default function Prize() {
  const [formData, setFormData] = useState<PrizeForm>({
    prizeName: "",
    prizeType: "",
    prizeValue: "",
  });

  // 🔹 Dummy data for initial prizes
  const [prizes, setPrizes] = useState<PrizeForm[]>([
    {
      prizeName: "Amazon Gift Card",
      prizeType: "Voucher",
      prizeValue: "₹1000",
    },
    { prizeName: "iPad Mini", prizeType: "Gadget", prizeValue: "1 Unit" },
    {
      prizeName: "Spotify Premium",
      prizeType: "Subscription",
      prizeValue: "6 Months",
    },
  ]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    if (!formData.prizeName || !formData.prizeType || !formData.prizeValue)
      return;
    setPrizes((prev) => [...prev, formData]);
    setFormData({ prizeName: "", prizeType: "", prizeValue: "" });
  };

  const handleCancel = () => {
    setFormData({ prizeName: "", prizeType: "", prizeValue: "" });
  };

  const handleDelete = (index: number) => {
    setPrizes((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="mt-6 mx-auto p-4 space-y-8">
      {/* Prize Form */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="space-y-2 w-3/4">
          <Label htmlFor="prizeType" className="text-sm font-medium text-white">
            Prize Type
          </Label>
          <Input
            id="prizeType"
            name="prizeType"
            type="text"
            value={formData.prizeType}
            onChange={handleInputChange}
            placeholder="Enter Prize Type"
            className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-500"
          />
        </div>
        <div className="space-y-2 w-3/4">
          <Label
            htmlFor="prizeValue"
            className="text-sm font-medium text-white"
          >
            Prize Value
          </Label>
          <Input
            id="prizeValue"
            name="prizeValue"
            value={formData.prizeValue}
            onChange={handleInputChange}
            placeholder="Enter Prize Value"
            className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-500"
          />
        </div>
      </div>
      <div className="flex justify-end gap-3">
        <Button
          variant="outline"
          onClick={handleCancel}
          className="bg-transparent border-zinc-700 text-white hover:bg-zinc-800"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          Add
        </Button>
      </div>

      {/* Existing Prizes Section */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-white">Existing Prizes</h2>
        <p className="text-sm text-zinc-400">
          Manage and monitor all Existing Prizes
        </p>

        {prizes.map((prize, index) => (
          <div
            key={index}
            className="bg-zinc-900 border border-zinc-800 rounded-lg p-4 flex justify-between items-center"
          >
            <div className="space-y-1 text-white gap-2">
              <p>
                <span className="font-semibold">Prize Type:</span>{" "}
                {prize.prizeType}
              </p>
              <p>
                <span className="font-semibold">Prize Value:</span>{" "}
                {prize.prizeValue}
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                className="bg-zinc-200 text-zinc-800 hover:bg-zinc-300"
              >
                <Edit className="h-4 w-4 mr-1" /> Edit
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleDelete(index)}
              >
                <Trash2 className="h-4 w-4 mr-1" /> Delete
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
