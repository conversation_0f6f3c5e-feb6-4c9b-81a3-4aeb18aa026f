"use client";
import { TabNavigation } from "@/components/constant/tab-navigation";
import MainLayout from "@/layouts/main-layout";
import React, { useState } from "react";
import ExamAds from "./exam-ads";
import ExamPrizes from "./exam-prizes";
import MainAds from "./main-ads";
import NoticeBoard from "./notice-board";

const tabs = [
  { id: "noticeBoard", label: "Notice Board" },
  {
    id: "examPrizes",
    label: "Exam Prizes",
  },
  {
    id: "examAds",
    label: "Exam ads",
  },
  {
    id: "mainAds",
    label: "Main ads",
  },
];

export default function SponsorAdsPage() {
  const [activeTab, setActiveTab] = useState<string>("noticeBoard");

  const renderTabContent = () => {
    switch (activeTab) {
      case "noticeBoard":
        return <NoticeBoard />;
      case "examPrizes":
        return <ExamPrizes />;
      case "examAds":
        return <ExamAds />;
      case "mainAds":
        return <MainAds />;
      default:
        return null;
    }
  };

  return (
    <MainLayout>
      <h1 className="text-2xl font-bold mb-4 text-text-primary">
        Sponsor & Ad Management
      </h1>
      <TabNavigation
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
      {renderTabContent()}
    </MainLayout>
  );
}
