"use client";

import {
  Column,
  DynamicDataTable,
  StatusBadge,
} from "@/components/constant/dynamic-data-table";
import MainLayout from "@/layouts/main-layout";
import React from "react";

interface UserLog {
  userId: string;
  name: string;
  role: "Admin" | "Content";
  time: string;
  operation: string;
}

// Dummy data
const sampleUserLogs: UserLog[] = [
  {
    userId: "001",
    name: "<PERSON><PERSON><PERSON>",
    role: "Admin",
    time: "20-01-2024, 16:00:00",
    operation: "Generated something in creator team",
  },
  {
    userId: "002",
    name: "<PERSON><PERSON><PERSON>",
    role: "Admin",
    time: "20-01-2024, 16:00:00",
    operation: "Generated something in creator team",
  },
  {
    userId: "003",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "<PERSON>min",
    time: "20-01-2024, 16:00:00",
    operation: "Collaborated on a new feature design",
  },
  {
    userId: "004",
    name: "<PERSON>",
    role: "Admin",
    time: "20-01-2024, 16:00:00",
    operation: "Conducted user research and gathered insights",
  },
  {
    userId: "005",
    name: "<PERSON><PERSON>",
    role: "Content",
    time: "20-01-2024, 16:00:00",
    operation: "Designed a prototype for testing",
  },
  {
    userId: "006",
    name: "Ganga",
    role: "Content",
    time: "20-01-2024, 16:00:00",
    operation: "Refined UI components for better usability",
  },
  {
    userId: "007",
    name: "Sree",
    role: "Content",
    time: "20-01-2024, 16:00:00",
    operation: "Presented findings to the stakeholders",
  },
  {
    userId: "008",
    name: "Aditi",
    role: "Content",
    time: "20-01-2024, 16:00:00",
    operation: "Collaborated with developers on new updates",
  },
];

// Columns
const userLogColumns: Column<UserLog>[] = [
  { key: "userId", label: "User ID", sortable: true },
  { key: "name", label: "Name", sortable: true },
  {
    key: "role",
    label: "Role",
    sortable: true,
    render: (value) => (
      <StatusBadge status={value} /> // ✅ same badge style (green/blue dots)
    ),
  },
  { key: "time", label: "Time", sortable: true },
  { key: "operation", label: "Operation", sortable: false },
];

export default function AuditLogPage() {
  const handleRowClick = (row: UserLog) => {
    console.log("Transaction clicked:", row.userId);
  };

  return (
    <MainLayout>
      <h1 className="text-2xl font-bold text-text-primary"> Audit Log</h1>
      <div className="grid p-4">
        <DynamicDataTable<UserLog>
          data={sampleUserLogs}
          columns={userLogColumns}
          onRowClick={handleRowClick}
        />
      </div>
    </MainLayout>
  );
}
