"use client";

import MainLayout from "@/layouts/main-layout";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, Edit, FileText } from "lucide-react";
import AddModal from "@/components/constant/addModal";
import Image from "next/image";

// -------------------- Types --------------------
interface Track {
  id: number;
  title: string;
  image: string;
}

interface Subject {
  id: number;
  name: string;
  color: string; // Tailwind class
}

interface Chapter {
  id: number;
  title: string;
  pages: number;
  image: string;
}

type ViewType = "tracks" | "subjects" | "chapters";

interface ModalField {
  id: string;
  label: string;
  type: "text" | "file";
}

interface ModalConfig {
  title: string;
  fields: ModalField[];
}

// -------------------- Dummy Data --------------------
const tracksData: Track[] = [
  { id: 1, title: "Technology", image: "/placeholder-2g22n.png" },
  { id: 2, title: "Java Programming", image: "/java-code-snippet.png" },
  { id: 3, title: "World Geography", image: "/placeholder-aen5x.png" },
  {
    id: 4,
    title: "Digital Marketing",
    image: "/digital-marketing-analytics.png",
  },
  { id: 5, title: "Data Science", image: "/data-science-charts-graphs.jpg" },
  { id: 6, title: "Web Development", image: "/web-development-coding.png" },
];

const subjectsData: Subject[] = [
  { id: 1, name: "JavaScript", color: "bg-yellow-500" },
  { id: 2, name: "React", color: "bg-blue-500" },
  { id: 3, name: "Node.js", color: "bg-green-500" },
  { id: 4, name: "Python", color: "bg-blue-600" },
  { id: 5, name: "HTML", color: "bg-orange-500" },
  { id: 6, name: "CSS", color: "bg-blue-400" },
  { id: 7, name: "MongoDB", color: "bg-green-600" },
  { id: 8, name: "TypeScript", color: "bg-blue-700" },
  { id: 9, name: "Next.js", color: "bg-black" },
];

const chaptersData: Chapter[] = [
  {
    id: 1,
    title: "Introduction to Java & Basics",
    pages: 12,
    image: "/java-code-snippet.png",
  },
  { id: 2, title: "OOP Concepts", pages: 15, image: "/placeholder-2g22n.png" },
  {
    id: 3,
    title: "Advanced Topics",
    pages: 10,
    image: "/web-development-coding.png",
  },
];

// -------------------- Component --------------------
export default function ContentPage() {
  const [currentView, setCurrentView] = useState<ViewType>("tracks");
  const [selectedTrack, setSelectedTrack] = useState<number | null>(null);
  const [selectedSubject, setSelectedSubject] = useState<number | null>(null);
  const [open, setOpen] = useState(false);
  const [modalConfig, setModalConfig] = useState<ModalConfig | null>(null);

  const router = useRouter();

  const handleAddNew = (view: Exclude<ViewType, "tracks">) => {
    if (view === "subjects") {
      setModalConfig({
        title: "Add Subject",
        fields: [
          { id: "name", label: "Subject Name", type: "text" },
          { id: "icon", label: "Subject Icon", type: "file" },
        ],
      });
    } else if (view === "chapters") {
      setModalConfig({
        title: "Add Chapter",
        fields: [
          { id: "title", label: "Chapter Title", type: "text" },
          // Remove the "pages" field or change its type to "text" or "file" if needed
          { id: "image", label: "Chapter Image", type: "file" },
        ],
      });
    }
    setOpen(true);
  };

  const handleBack = () => {
    if (currentView === "chapters") {
      setCurrentView("subjects");
      setSelectedSubject(null);
    } else if (currentView === "subjects") {
      setCurrentView("tracks");
      setSelectedTrack(null);
      router.push("/content");
    }
  };

  // -------------------- Renderers --------------------
  const renderTracks = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {tracksData.map((track) => (
        <div
          key={track.id}
          className="relative bg-slate-800 rounded-lg overflow-hidden border border-slate-700 hover:border-slate-600 transition-colors cursor-pointer"
          onClick={() => {
            setSelectedTrack(track.id);
            setCurrentView("subjects");
            router.push(`/content?${track.title}`);
          }}
        >
          <Image
            src={track.image}
            alt={track.title}
            width={500}
            height={200}
            className="w-full h-48 object-cover"
          />
          <div className="absolute inset-0 flex items-center justify-center bg-black/40">
            <h3 className="text-white font-semibold text-lg text-center">
              {track.title}
            </h3>
          </div>
        </div>
      ))}
    </div>
  );

  const renderSubjects = () => (
    <>
      <button
        onClick={handleBack}
        className="flex items-center text-slate-400 hover:text-white mb-4"
      >
        <ArrowLeft className="w-5 h-5 mr-2" /> Back
      </button>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {subjectsData.map((subject) => (
          <div
            key={subject.id}
            className="flex flex-col items-center group cursor-pointer"
            onClick={() => {
              setSelectedSubject(subject.id);
              setCurrentView("chapters");
              router.push(`/content?${selectedTrack}&${subject.name}`);
            }}
          >
            <div
              className={`w-16 h-16 ${subject.color} rounded-full flex items-center justify-center mb-3 group-hover:scale-110 transition-transform`}
            >
              <FileText className="w-8 h-8 text-white" />
            </div>
            <span className="text-white text-sm font-medium text-center">
              {subject.name}
            </span>
          </div>
        ))}
      </div>
    </>
  );

  const renderChapters = () => (
    <>
      <button
        onClick={handleBack}
        className="flex items-center text-slate-400 hover:text-white mb-4"
      >
        <ArrowLeft className="w-5 h-5 mr-2" /> Back
      </button>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {chaptersData.map((chapter, index) => (
          <div
            key={chapter.id}
            className="bg-slate-900 rounded-xl overflow-hidden border border-slate-700 hover:border-slate-500 transition-colors shadow-lg cursor-pointer"
          >
            <Image
              src={chapter.image}
              alt={chapter.title}
              className="w-full h-36 object-cover"
              width={500}
              height={200}
            />
            <div className="p-4">
              <div className="flex items-center justify-between text-slate-400 text-sm mb-1">
                <span>Chapter {index + 1}</span>
                <Edit className="w-4 h-4 cursor-pointer hover:text-white" />
              </div>
              <p className="text-slate-400 text-xs mb-2">
                {chapter.pages} Pages
              </p>
              <h3 className="text-white font-semibold text-base mb-3">
                {chapter.title}
              </h3>
              <button
                className="px-3 py-1 bg-slate-200 text-slate-900 rounded-md text-sm font-medium"
                onClick={() =>
                  router.push(
                    `/content/addContent?${selectedTrack}&${selectedSubject}/${chapter.id}`
                  )
                }
              >
                Add / Edit Pages
              </button>
            </div>
          </div>
        ))}
      </div>
    </>
  );

  // -------------------- Render --------------------
  return (
    <MainLayout>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-white">Content</h1>
          <p className="text-text-secondary mt-1 capitalize">{currentView}</p>
        </div>
        {currentView !== "tracks" && (
          <button
            className="px-4 py-2 bg-[#016E01] text-white rounded-lg hover:bg-[#016E01]/90 transition"
            onClick={() => handleAddNew(currentView)}
          >
            + Add New {currentView === "subjects" ? "Subject" : "Chapter"}
          </button>
        )}
      </div>

      {open && modalConfig && (
        <AddModal
          title={modalConfig.title}
          fields={modalConfig.fields}
          onClose={() => setOpen(false)}
          onSubmit={(data: Record<string, string | File>) => {
            console.log("Form submitted:", data);
            setOpen(false);
          }}
        />
      )}

      <div className="space-y-6">
        {currentView === "tracks" && renderTracks()}
        {currentView === "subjects" && renderSubjects()}
        {currentView === "chapters" && renderChapters()}
      </div>
    </MainLayout>
  );
}
