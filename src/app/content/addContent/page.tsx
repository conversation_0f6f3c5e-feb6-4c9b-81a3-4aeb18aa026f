"use client";
import MainLayout from "@/layouts/main-layout";
import React, { useRef, useState } from "react";
import { RichTextEditor } from "./rich-text-editor";
import { TabNavigation } from "@/components/constant/tab-navigation";
import { Upload } from "lucide-react";
import { Button } from "@/components/ui/button";

const tabs = [
  { id: "content", label: "Content" },
  {
    id: "questions",
    label: "Questions",
  },
];

export default function Page() {
  const [activeTab, setActiveTab] = useState<string>("content");
  const [pages, setPages] = useState([
    { id: "1", title: "Page 1", content: "" },
  ]);

  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadError, setUploadError] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [typeFilter, setTypeFilter] = useState("textOnly");

  const handleChangePageContent = (index: number, content: string) => {
    setPages((prev) => {
      const updated = [...prev];
      updated[index] = { ...updated[index], content };
      return updated;
    });
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validExtensions = [".xls", ".xlsx"];
    const fileExtension = file.name
      .toLowerCase()
      .slice(file.name.lastIndexOf("."));

    if (!validExtensions.includes(fileExtension)) {
      setUploadError("Please upload a valid Excel file (.xls or .xlsx)");
      setUploadedFile(null);
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      setUploadError("File size must be less than 10MB");
      setUploadedFile(null);
      return;
    }

    setUploadError("");
    setUploadedFile(file);
  };

  const handleAddPage = () => {
    const newPage = {
      id: Date.now().toString(),
      title: `Page ${pages.length + 1}`,
      content: "",
    };
    setPages((prev) => [...prev, newPage]);
    setCurrentPageIndex(pages.length);
  };

  const handleDeletePage = () => {
    if (pages.length <= 1) return;
    const updated = pages.filter((_, i) => i !== currentPageIndex);
    setPages(updated);
    setCurrentPageIndex(Math.max(0, currentPageIndex - 1));
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "content":
        return (
          <RichTextEditor
            pages={pages}
            currentPageIndex={currentPageIndex}
            typeFilter={typeFilter}
            onChangePageContent={handleChangePageContent}
            onAddPage={handleAddPage}
            onDeletePage={handleDeletePage}
            onChangePageIndex={setCurrentPageIndex}
            setTypeFilter={setTypeFilter}
          />
        );
      case "questions":
        return (
          <div className="space-y-6 p-6">
            <div className="border-2 border-dashed border-zinc-700 rounded-lg p-6 text-center hover:border-primary/50 transition-colors">
              <input
                ref={fileInputRef}
                type="file"
                accept=".xls,.xlsx"
                onChange={handleFileSelect}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="h-10 w-10 text-gray-400 mb-3" />
                <span className="text-white text-sm mb-1">
                  Click to upload or drag and drop
                </span>
                <span className="text-gray-500 text-xs">
                  XLS or XLSX (max 10MB)
                </span>
              </label>
            </div>

            {uploadedFile && (
              <div className="mt-3 p-3 bg-green-900/20 border border-green-600/30 rounded-md">
                <p className="text-green-400 text-sm flex items-center gap-2">
                  <span className="font-medium">Selected:</span>
                  <span>{uploadedFile.name}</span>
                  <span className="text-gray-400">
                    ({(uploadedFile.size / 1024).toFixed(2)} KB)
                  </span>
                </p>
              </div>
            )}

            {uploadError && (
              <div className="mt-3 p-3 bg-red-900/20 border border-red-600/30 rounded-md">
                <p className="text-red-400 text-sm">{uploadError}</p>
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <MainLayout>
      <div className="flex items-center justify-between mb-4 w-full p-4 rounded-lg">
        <div>
          <h2 className="text-xl font-semibold text-white">Content</h2>
        </div>
        <div className="flex gap-2">
          <Button
            className="px-4 py-2 bg-white text-black rounded-sm hover:bg-white/60"
            onClick={() => console.log("Save Draft")}
          >
            Save
          </Button>
          <Button
            className="px-4 py-2 bg-[#016E01] text-white rounded-sm hover:bg-[#016E01]/60"
            onClick={() => console.log("Publish")}
          >
            Publish
          </Button>
        </div>
      </div>
      <TabNavigation
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {renderTabContent()}
    </MainLayout>
  );
}
