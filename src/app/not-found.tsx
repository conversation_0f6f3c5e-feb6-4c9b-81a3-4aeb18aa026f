"use client";

import { motion } from "framer-motion";
import { Home, Search, ArrowLeft, Zap, AlertTriangle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function NotFound() {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="min-h-screen bg-[#0E0E0E] text-white flex items-center justify-center relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Orbs */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full opacity-10"
            style={{
              background: `linear-gradient(45deg, ${
                i % 3 === 0 ? "#D7E1E4" : i % 3 === 1 ? "#D1E4EB" : "#016E01"
              }, transparent)`,
              width: `${Math.random() * 200 + 100}px`,
              height: `${Math.random() * 200 + 100}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              repeatType: "reverse",
            }}
          />
        ))}

        {/* Grid Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `
                linear-gradient(rgba(215, 225, 228, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(215, 225, 228, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: "50px 50px",
            }}
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        {/* Animated 404 Number */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            duration: 1,
          }}
          className="mb-8"
        >
          <div className="relative">
            <h1 className="text-[12rem] md:text-[16rem] font-bold leading-none bg-gradient-to-r from-[#D7E1E4] via-[#D1E4EB] to-[#016E01] bg-clip-text text-transparent">
              404
            </h1>
            
            {/* Glitch Effect */}
            <motion.div
              className="absolute inset-0 text-[12rem] md:text-[16rem] font-bold leading-none text-[#016E01] opacity-20"
              animate={{
                x: [0, -2, 2, 0],
                y: [0, 1, -1, 0],
              }}
              transition={{
                duration: 0.2,
                repeat: Infinity,
                repeatType: "reverse",
                repeatDelay: 3,
              }}
            >
              404
            </motion.div>
          </div>
        </motion.div>

        {/* Warning Icon with Pulse */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="mb-6"
        >
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-[#D7E1E4]/20 to-[#016E01]/20 border border-[#D7E1E4]/30"
          >
            <AlertTriangle className="w-10 h-10 text-[#D7E1E4]" />
          </motion.div>
        </motion.div>

        {/* Title and Description */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.6 }}
          className="mb-8"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-[#D7E1E4] mb-4">
            Oops! Page Not Found
          </h2>
          <p className="text-lg md:text-xl text-[#9ca3af] max-w-2xl mx-auto leading-relaxed">
            The page you're looking for seems to have vanished into the digital void. 
            Don't worry, even the best explorers sometimes take a wrong turn.
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => router.push("/dashboard")}
            className="group flex items-center gap-3 px-8 py-4 bg-[#D7E1E4] text-[#1a1a1a] rounded-xl font-semibold text-lg hover:bg-[#c1cdd2] transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <Home className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
            Go Home
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => router.back()}
            className="group flex items-center gap-3 px-8 py-4 bg-[#D1E4EB]/20 text-[#D1E4EB] border border-[#D1E4EB]/30 rounded-xl font-semibold text-lg hover:bg-[#D1E4EB]/30 transition-all duration-300"
          >
            <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300" />
            Go Back
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => router.push("/content")}
            className="group flex items-center gap-3 px-8 py-4 bg-[#016E01]/20 text-[#016E01] border border-[#016E01]/30 rounded-xl font-semibold text-lg hover:bg-[#016E01]/30 transition-all duration-300"
          >
            <Search className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
            Explore Content
          </motion.button>
        </motion.div>

        {/* Fun Fact */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2, duration: 0.8 }}
          className="mt-12 p-6 bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl max-w-md mx-auto"
        >
          <div className="flex items-center gap-3 mb-2">
            <Zap className="w-5 h-5 text-[#016E01]" />
            <span className="text-[#D7E1E4] font-semibold">Fun Fact</span>
          </div>
          <p className="text-[#9ca3af] text-sm">
            The HTTP 404 error was named after room 404 at CERN, where the original web servers were located. 
            Though this is actually a myth! 🤓
          </p>
        </motion.div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-[#D7E1E4] rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100],
              opacity: [0.3, 0, 0.3],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>
    </div>
  );
}
