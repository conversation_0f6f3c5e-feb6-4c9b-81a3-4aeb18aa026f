"use client";

import StatsCard from "@/components/constant/states-card";
import TestResultCard from "@/components/constant/test-results-card";
import MainLayout from "@/layouts/main-layout";
import { useRouter } from "next/navigation";
import React from "react";

// Types for stats
interface Stat {
  title: string;
  value: number;
  change: number;
  isPositive: boolean;
}

// Types for test results
interface TestResult {
  id: number;
  testName: string;
  date: string;
  sponsor: string;
  scholars: number;
  testType: "weekly" | "quarterly";
  status: "published" | "unpublished";
}

// Dummy data for stats
const statsData: Stat[] = [
  { title: "Total Test", value: 8452, change: 5, isPositive: true },
  { title: "Published Results", value: 1230, change: 3, isPositive: true },
  { title: "Total Scholars", value: 2345, change: 1, isPositive: false },
  { title: "Unpublished Result", value: 678, change: 8, isPositive: true },
];

// Dummy data for test results
const testResults: TestResult[] = [
  {
    id: 1,
    testName: "Weekly Test - Week 45",
    date: "08-11-2024",
    sponsor: "Amazon",
    scholars: 1250,
    testType: "weekly",
    status: "published",
  },
  {
    id: 2,
    testName: "Weekly Test - Week 45",
    date: "06-08-2024",
    sponsor: "Amazon",
    scholars: 1250,
    testType: "weekly",
    status: "published",
  },
  {
    id: 3,
    testName: "Quarterly Test - Week 45",
    date: "15-12-2024",
    sponsor: "Amazon",
    scholars: 1180,
    testType: "quarterly",
    status: "unpublished",
  },
];

export default function ResultsPage() {
  const router = useRouter()
  const handleViewResults = (testId: number) => {
    console.log(`View results for test ${testId}`);
    router.push(`/results/${testId}`)
  };

  return (
    <MainLayout>
      <div className="text-white p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              change={stat.change}
              isPositive={stat.isPositive}
            />
          ))}
        </div>

        {/* Test Results Section */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Test Result</h2>
          <p className="text-gray-400 mb-6">
            Manage and monitor all weekly and quarterly tests
          </p>
        </div>

        {/* Test Result Cards */}
        <div className="space-y-4">
          {testResults.map((test) => (
            <TestResultCard
              key={test.id}
              testName={test.testName}
              date={test.date}
              sponsor={test.sponsor}
              scholars={test.scholars}
              testType={test.testType}
              status={test.status}
              onViewResults={() => handleViewResults(test.id)}
            />
          ))}
        </div>
      </div>
    </MainLayout>
  );
}
