"use client";
import { DynamicDataTable } from "@/components/constant/dynamic-data-table";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import MainLayout from "@/layouts/main-layout";
import { Calendar1Icon, Users, Upload, X } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useState, useRef } from "react";

interface Subscriber {
  id: string;
  name: string;
  email: string;
  status: "Active" | "Inactive";
  plan: "Weekly" | "Monthly" | "Quarterly";
  joined: string;
  lastActive: string;
}

interface PrizeCategory {
  id: string;
  prizeRange: string;
  sponsor: string;
  prizeType: string;
  prizeValue: string;
  document: string;
}

const subscriberColumns: {
  key: keyof Subscriber | "actions";
  label: string;
  sortable?: boolean;
  render?: (row: Subscriber) => React.ReactNode;
}[] = [
  { key: "name", label: "Name", sortable: true },
  { key: "email", label: "Email", sortable: true },
  {
    key: "status",
    label: "Status",
    render: (row) => (
      <span
        className={`px-3 py-1 rounded-md text-sm font-medium flex items-center gap-2 w-fit ${
          row.status === "Active"
            ? "bg-gray-800 text-green-400 border border-green-600"
            : "bg-gray-800 text-red-400 border border-red-600"
        }`}
      >
        <span
          className={`h-2 w-2 rounded-full ${
            row.status === "Active" ? "bg-green-500" : "bg-red-500"
          }`}
        />
        {row.status}
      </span>
    ),
  },
  { key: "plan", label: "Plan" },
  { key: "joined", label: "Joined" },
  { key: "lastActive", label: "Last Active" },
];

const prizeCategoryColumns: {
  key: keyof PrizeCategory | "actions";
  label: string;
  sortable?: boolean;
}[] = [
  { key: "prizeRange", label: "Prize Range", sortable: true },
  { key: "sponsor", label: "Sponsor", sortable: true },
  { key: "prizeType", label: "Prize Type", sortable: true },
  { key: "prizeValue", label: "Prize Value", sortable: true },
  { key: "actions", label: "Document" },
];

const sampleSubscribers: Subscriber[] = [
  {
    id: "001",
    name: "Adithyan Ullas",
    email: "<EMAIL>",
    status: "Active",
    plan: "Monthly",
    joined: "2024-01-15",
    lastActive: "2024-09-28",
  },
  {
    id: "002",
    name: "John Mathew",
    email: "<EMAIL>",
    status: "Active",
    plan: "Weekly",
    joined: "2024-02-20",
    lastActive: "2024-09-29",
  },
  {
    id: "003",
    name: "Kauishik",
    email: "<EMAIL>",
    status: "Inactive",
    plan: "Quarterly",
    joined: "2024-03-10",
    lastActive: "2024-08-15",
  },
  {
    id: "004",
    name: "Raja",
    email: "<EMAIL>",
    status: "Active",
    plan: "Monthly",
    joined: "2024-04-05",
    lastActive: "2024-09-30",
  },
  {
    id: "005",
    name: "Swaroop",
    email: "<EMAIL>",
    status: "Active",
    plan: "Weekly",
    joined: "2024-05-12",
    lastActive: "2024-09-27",
  },
];

const samplePrizeCategories: PrizeCategory[] = [
  {
    id: "1",
    prizeRange: "1-1",
    sponsor: "Amazon",
    prizeType: "Cash",
    prizeValue: "1,00,000",
    document: "certificate.pdf",
  },
  {
    id: "2",
    prizeRange: "2-2",
    sponsor: "Flipkart",
    prizeType: "Cash",
    prizeValue: "50,000",
    document: "certificate.pdf",
  },
  {
    id: "3",
    prizeRange: "3-3",
    sponsor: "Amazon",
    prizeType: "Cash",
    prizeValue: "30,000",
    document: "certificate.pdf",
  },
  {
    id: "4",
    prizeRange: "4-500",
    sponsor: "Amazon",
    prizeType: "Coupon",
    prizeValue: "1000",
    document: "voucher.pdf",
  },
  {
    id: "5",
    prizeRange: "501-1000",
    sponsor: "Hero",
    prizeType: "Bike",
    prizeValue: "1000",
    document: "details.pdf",
  },
  {
    id: "6",
    prizeRange: "1001-2000",
    sponsor: "Hero",
    prizeType: "Bike",
    prizeValue: "1000",
    document: "details.pdf",
  },
  {
    id: "7",
    prizeRange: "2001-3000",
    sponsor: "Hero",
    prizeType: "Bike",
    prizeValue: "1000",
    document: "details.pdf",
  },
];

export default function ResultsDetailPage() {
  const router = useRouter();
  const [uploadModalOpen, setUploadModalOpen] = useState<boolean>(false);
  const [selectedPrizeId, setSelectedPrizeId] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadError, setUploadError] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleRowClick = (row: Subscriber | PrizeCategory) => {
    console.log("Row clicked:", row);
  };

  const handlePrizeAction = (id: string) => {
    setSelectedPrizeId(id);
    setUploadModalOpen(true);
    setUploadError("");
    setUploadedFile(null);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validExtensions = [".xls", ".xlsx"];
    const fileExtension = file.name
      .toLowerCase()
      .slice(file.name.lastIndexOf("."));

    if (!validExtensions.includes(fileExtension)) {
      setUploadError("Please upload a valid Excel file (.xls or .xlsx)");
      setUploadedFile(null);
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      setUploadError("File size must be less than 10MB");
      setUploadedFile(null);
      return;
    }

    setUploadError("");
    setUploadedFile(file);
  };

  const handleUploadSubmit = () => {
    if (!uploadedFile) {
      setUploadError("Please select a file to upload");
      return;
    }

    // Here you would typically upload the file to your server
    console.log(
      "Uploading file:",
      uploadedFile.name,
      "for prize category:",
      selectedPrizeId
    );

    // Simulate upload
    alert(
      `File "${uploadedFile.name}" uploaded successfully for prize category ${selectedPrizeId}`
    );

    // Close modal and reset
    setUploadModalOpen(false);
    setUploadedFile(null);
    setSelectedPrizeId(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleCloseModal = () => {
    setUploadModalOpen(false);
    setUploadedFile(null);
    setUploadError("");
    setSelectedPrizeId(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <MainLayout>
      <div className="flex flex-col w-full">
        <Card className="bg-white/4 w-full border border-zinc-800 rounded-lg shadow-sm mt-5">
          <CardContent className="p-5">
            <h2 className="text-white font-semibold text-base mb-4">
              Exam Information
            </h2>

            <div className="flex justify-between items-center">
              <div className="text-white text-sm space-y-3">
                <div className="flex items-center">
                  <Calendar1Icon className="h-4 w-4 mr-2" />
                  <span>08-11-2024</span>
                </div>
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  <span>1,250 Scholars</span>
                </div>
              </div>

              <div className="flex gap-4">
                <Button
                  size="sm"
                  className="bg-green-700 text-white hover:bg-green-800 rounded-md px-5"
                >
                  Weekly
                </Button>
                <Button
                  size="sm"
                  className="bg-primary text-white hover:bg-cyan-600 rounded-md px-5"
                >
                  Publish
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="mt-8">
          <h3 className="text-white font-semibold text-lg mb-4">
            Prize Categories
          </h3>
          <DynamicDataTable<PrizeCategory>
            data={samplePrizeCategories}
            columns={prizeCategoryColumns}
            onRowClick={handleRowClick}
            renderActions={(row) => (
              <div className="flex gap-2">
                <button
                  onClick={() => handlePrizeAction(row.id)}
                  className="px-3 py-1 w-20 rounded bg-primary/5 text-white hover:bg-primary/10 border border-primary/20"
                >
                  +
                </button>
              </div>
            )}
          />
        </div>

        <div className="mt-8">
          <h3 className="text-white font-semibold text-lg mb-4">
            Prize Rankings
          </h3>
          <DynamicDataTable<Subscriber>
            data={sampleSubscribers}
            columns={subscriberColumns}
            onRowClick={handleRowClick}
          />
        </div>

        {/* Upload Modal */}
        {uploadModalOpen && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="bg-zinc-900 border border-zinc-800 rounded-lg p-6 w-full max-w-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-white font-semibold text-lg">
                  Upload Excel Document
                </h3>
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="mb-4">
                <p className="text-gray-400 text-sm mb-4">
                  Upload an Excel file (.xls or .xlsx) for Prize Category #
                  {selectedPrizeId}
                </p>

                <div className="border-2 border-dashed border-zinc-700 rounded-lg p-6 text-center hover:border-primary/50 transition-colors">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".xls,.xlsx"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-upload"
                  />
                  <label
                    htmlFor="file-upload"
                    className="cursor-pointer flex flex-col items-center"
                  >
                    <Upload className="h-10 w-10 text-gray-400 mb-3" />
                    <span className="text-white text-sm mb-1">
                      Click to upload or drag and drop
                    </span>
                    <span className="text-gray-500 text-xs">
                      XLS or XLSX (max 10MB)
                    </span>
                  </label>
                </div>

                {uploadedFile && (
                  <div className="mt-3 p-3 bg-green-900/20 border border-green-600/30 rounded-md">
                    <p className="text-green-400 text-sm flex items-center gap-2">
                      <span className="font-medium">Selected:</span>
                      <span>{uploadedFile.name}</span>
                      <span className="text-gray-400">
                        ({(uploadedFile.size / 1024).toFixed(2)} KB)
                      </span>
                    </p>
                  </div>
                )}

                {uploadError && (
                  <div className="mt-3 p-3 bg-red-900/20 border border-red-600/30 rounded-md">
                    <p className="text-red-400 text-sm">{uploadError}</p>
                  </div>
                )}
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={handleCloseModal}
                  className="flex-1 bg-zinc-800 text-white hover:bg-zinc-700"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUploadSubmit}
                  disabled={!uploadedFile}
                  className="flex-1 bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Upload
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
