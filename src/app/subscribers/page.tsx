"use client";

import MainLayout from "@/layouts/main-layout";
import React, { useState } from "react";
import { Eye, Edit, Trash2, Search, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import FilterSelect from "@/components/constant/filter-select";
import { useRouter } from "next/navigation";
import { DynamicDataTable } from "@/components/constant/dynamic-data-table";

// Types
interface Subscriber {
  id: string;
  name: string;
  email: string;
  status: "Active" | "Inactive";
  plan: "Weekly" | "Monthly" | "Quarterly";
  joined: string;
  lastActive: string;
}

// Dummy data
const sampleSubscribers: Subscriber[] = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    status: "Active",
    plan: "Monthly",
    joined: "30-05-24",
    lastActive: "28-05-24",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "k<PERSON><EMAIL>",
    status: "Inactive",
    plan: "Weekly",
    joined: "30-05-24",
    lastActive: "28-05-24",
  },
  {
    id: "3",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    status: "Active",
    plan: "Monthly",
    joined: "30-05-24",
    lastActive: "28-05-24",
  },
  {
    id: "4",
    name: "Swaroop",
    email: "<EMAIL>",
    status: "Active",
    plan: "Quarterly",
    joined: "30-05-24",
    lastActive: "28-05-24",
  },
  {
    id: "5",
    name: "Ashiq",
    email: "<EMAIL>",
    status: "Inactive",
    plan: "Monthly",
    joined: "30-05-24",
    lastActive: "28-05-24",
  },
  {
    id: "6",
    name: "Deleep",
    email: "<EMAIL>",
    status: "Inactive",
    plan: "Monthly",
    joined: "30-05-24",
    lastActive: "28-05-24",
  },
  {
    id: "7",
    name: "Ammu",
    email: "<EMAIL>",
    status: "Active",
    plan: "Monthly",
    joined: "30-05-24",
    lastActive: "28-05-24",
  },
];

// Table column definitions
const subscriberColumns: {
  key: keyof Subscriber | "actions";
  label: string;
  sortable?: boolean;
  render?: (row: Subscriber) => React.ReactNode;
}[] = [
  { key: "name", label: "Name", sortable: true },
  { key: "email", label: "Email", sortable: true },
  {
    key: "status",
    label: "Status",
    render: (row) => (
      <span
        className={`px-3 py-1 rounded-md text-sm font-medium flex items-center gap-2 w-fit ${
          row.status === "Active"
            ? "bg-gray-800 text-green-400 border border-green-600"
            : "bg-gray-800 text-red-400 border border-red-600"
        }`}
      >
        <span
          className={`h-2 w-2 rounded-full ${
            row.status === "Active" ? "bg-green-500" : "bg-red-500"
          }`}
        />
        {row.status}
      </span>
    ),
  },
  { key: "plan", label: "Plan" },
  { key: "joined", label: "Joined" },
  { key: "lastActive", label: "Last Active" },
  { key: "actions", label: "Action" },
];

export default function SubscribersPage() {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const router = useRouter();

  const handleActionButtonClick = (type: string, id: string) => {
    switch (type) {
      case "View":
        router.push(`/subscribers/${id}`);
        break;
      case "Edit":
        console.log("Edit clicked for", id);
        break;
      case "Suspend":
        console.log("Suspend clicked for", id);
        break;
      default:
        console.log("Unknown action", type, id);
    }
  };

  const handleRowClick = (row: Subscriber) => {
    console.log("Row clicked:", row);
  };

  return (
    <MainLayout>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">Subscribers</h1>
          <p className="text-text-secondary mt-1">
            Manage and monitor all platform users
          </p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 animate-fadeIn">
        {/* Search */}
        <div className="flex-1 relative group">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-text-muted group-focus-within:text-primary transition-colors" />
          </div>
          <Input
            type="text"
            placeholder="Search users by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 bg-bg-card border border-[#fff]/40 text-text-primary placeholder:text-text-muted 
             hover:bg-bg-card-hover hover:border-primary/40 
             focus:border-primary focus:ring-2 focus:ring-primary/20 
             transition-all duration-200 rounded-lg shadow-sm"
          />
        </div>

        {/* Filters */}
        <div className="flex gap-3 items-center">
          <div className="flex items-center gap-2 text-text-secondary text-sm">
            <Filter className="h-4 w-4" />
            <span>Filters:</span>
          </div>

          {/* Status Filter */}
          <FilterSelect
            placeholder="Status"
            value={statusFilter}
            onChange={setStatusFilter}
            options={[
              { value: "all", label: "All Status" },
              { value: "active", label: "Active" },
              { value: "inactive", label: "Inactive" },
            ]}
          />

          {/* Role Filter (dummy for now) */}
          <FilterSelect
            placeholder="Role"
            value={roleFilter}
            onChange={setRoleFilter}
            options={[
              { value: "all", label: "All Roles" },
              { value: "admin", label: "Admin" },
              { value: "editor", label: "Editor" },
              { value: "viewer", label: "Viewer" },
            ]}
          />
        </div>
      </div>

      {/* Data Table */}
      <div className="space-y-4 full-width-container">
        <DynamicDataTable<Subscriber>
          data={sampleSubscribers}
          columns={subscriberColumns}
          onRowClick={handleRowClick}
          renderActions={(row) => (
            <div className="flex gap-2">
              <button
                onClick={() => handleActionButtonClick("View", row.id)}
                className="px-3 py-1 rounded bg-gray-200 text-black hover:bg-gray-300"
              >
                <Eye className="h-4 w-4 inline mr-1" /> View
              </button>
              <button
                onClick={() => handleActionButtonClick("Edit", row.id)}
                className="px-3 py-1 rounded bg-gray-200 text-black hover:bg-gray-300"
              >
                <Edit className="h-4 w-4 inline mr-1" /> Edit
              </button>
              <button
                onClick={() => handleActionButtonClick("Suspend", row.id)}
                className="px-3 py-1 rounded bg-gray-500 text-red-600 hover:bg-red-700 hover:text-white"
              >
                <Trash2 className="h-4 w-4 inline mr-1" /> Suspend
              </button>
            </div>
          )}
        />
      </div>
    </MainLayout>
  );
}
