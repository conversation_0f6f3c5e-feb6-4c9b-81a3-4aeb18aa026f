"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Mail,
  Phone,
  Calendar,
  Activity,
  SquarePen,
} from "lucide-react";

interface SubscriberProfileHeaderProps {
  user: {
    id: string;
    name: string;
    email: string;
    phone: string;
    avatar?: string;
    joinedDate: string;
    lastActive: string;
    status: "Active" | "Inactive";
    plan: "Pro" | "Basic" | "Premium";
  };
  onAddCredit?: () => void;
  onEditUser?: () => void;
  showActions?: boolean;
}

export function SubscriberProfileHeader({
  user,
  onAddCredit,
  onEditUser,
  showActions = true,
}: SubscriberProfileHeaderProps) {
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <Card className="bg-white/5 backdrop-blur-md border-white/10 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          <Avatar className="h-16 w-16">
            <AvatarImage
              src={user.avatar || "/placeholder.svg"}
              alt={user.name}
            />
            <AvatarFallback className="bg-white/10 text-white text-lg">
              {getInitials(user.name)}
            </AvatarFallback>
          </Avatar>

          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-semibold text-white">{user.name}</h2>
              <Badge
                variant="secondary"
                className={`${
                  user.status === "Active"
                    ? "bg-white/4 border-white/40"
                    : "bg-white/4 text-red-400 border-red-500/30"
                }`}
              >
                <div
                  className={`w-2 h-2 rounded-full mr-2 ${
                    user.status === "Active" ? "bg-green-400" : "bg-red-400"
                  }`}
                />
                {user.status}
              </Badge>
              <Badge variant="secondary" className="bg-white/4 border-white/40">
                <div className="w-2 h-2 rounded-full bg-primary mr-2" />
                {user.plan}
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-x-8 gap-y-2 text-sm text-gray-300">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span>{user.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span>{user.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Joined: {user.joinedDate}</span>
              </div>
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                <span>Last Active: {user.lastActive}</span>
              </div>
            </div>
          </div>
        </div>

        {showActions && (
          <div className="flex gap-3">
            <Button
              onClick={onAddCredit}
              className="bg-yellow-500 hover:bg-yellow-600 text-black font-medium"
            >
              <SquarePen className="h-4 w-4 mr-2" />
              Add Credit
            </Button>
            <Button
              onClick={onEditUser}
              className="bg-green-500 hover:bg-green-600 text-white font-medium"
            >
              <SquarePen className="h-4 w-4 mr-2" />
              Edit User
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
}
