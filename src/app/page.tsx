"use client";

import { Input } from "@/components/ui/input";
import AuthLayout from "@/layouts/auth-layout";
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { Auth, AuthResponse } from "@/models/auth";
import { useRouter } from "next/navigation";
import { loginUser } from "@/service/auth.service";
import { Button } from "@/components/ui/button"; // optional if you use shadcn/ui Button
import { useDispatch } from "react-redux";
import { setAuthUser } from "@/redux/constSlice";

export default function Home() {
  const [formData, setFormData] = useState<Auth>({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const dispatch = useDispatch()
  // handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  // toggle password visibility
  const togglePassword = () => setShowPassword((prev) => !prev);

  // handle login
  const handleAuthenticate = async () => {
    setLoading(true);
    setError(null);
    try {
      const data: AuthResponse = await loginUser(formData);

      if(data){
        router.push("/dashboard");
        dispatch(setAuthUser(data.user));
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("Login failed. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="flex flex-col gap-6 max-w-sm w-full mx-auto">
        <h1 className="text-3xl text-center font-bold">Admin Login</h1>
        <p className="text-center text-gray-500">
          Please enter your credentials to access the admin panel.
        </p>

        <div className="flex flex-col gap-4">
          <Input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="Email"
            required
          />

          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Password"
              required
              className="pr-10"
            />
            <button
              type="button"
              onClick={togglePassword}
              className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-gray-700"
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5" />
              ) : (
                <Eye className="h-5 w-5" />
              )}
            </button>
          </div>

          {error && <p className="text-sm text-red-500 text-center">{error}</p>}

          <Button
            onClick={handleAuthenticate}
            disabled={loading}
            className="w-full bg-primary text-white rounded-lg p-2 hover:bg-primary/80"
          >
            {loading ? "Logging in..." : "Login"}
          </Button>
        </div>
      </div>
    </AuthLayout>
  );
}
